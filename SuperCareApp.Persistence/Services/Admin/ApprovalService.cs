using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SuperCareApp.Application.Common.Interfaces.Admin;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Persistence.Context;

namespace SuperCareApp.Persistence.Services.Admin
{
    public class ApprovalService : IApprovalService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<ApprovalService> _logger;

        public ApprovalService(ApplicationDbContext context, ILogger<ApprovalService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<Result<IEnumerable<Approval>>> GetPendingApprovalsAsync()
        {
            try
            {
                var pendingApprovals = await _context
                    .Approvals.Where(a => !a.IsDeleted && a.IsApproved == null)
                    .Include(a => a.User)
                    .Include(a => a.Processor)
                    .OrderByDescending(a => a.CreatedAt)
                    .ToListAsync();

                _logger.LogInformation(
                    "Retrieved {Count} pending approvals",
                    pendingApprovals.Count
                );
                return Result.Success<IEnumerable<Approval>>(pendingApprovals);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending approvals");
                return Result.Failure<IEnumerable<Approval>>(
                    Error.Internal("Error getting pending approvals")
                );
            }
        }

        public async Task<Result<IEnumerable<Approval>>> GetApprovalsByUserIdAsync(Guid userId)
        {
            try
            {
                var userApprovals = await _context
                    .Approvals.Where(a => !a.IsDeleted && a.UserId == userId)
                    .Include(a => a.User)
                    .Include(a => a.Processor)
                    .OrderByDescending(a => a.CreatedAt)
                    .ToListAsync();

                _logger.LogInformation(
                    "Retrieved {Count} approvals for user {UserId}",
                    userApprovals.Count,
                    userId
                );
                return Result.Success<IEnumerable<Approval>>(userApprovals);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting approvals for user {UserId}", userId);
                return Result.Failure<IEnumerable<Approval>>(
                    Error.Internal($"Error getting approvals for user {userId}")
                );
            }
        }

        public async Task<Result<IEnumerable<Approval>>> GetApprovalsByTypeAsync(ApprovalType type)
        {
            try
            {
                var typeApprovals = await _context
                    .Approvals.Where(a => !a.IsDeleted && a.ApprovalType == type)
                    .Include(a => a.User)
                    .Include(a => a.Processor)
                    .OrderByDescending(a => a.CreatedAt)
                    .ToListAsync();

                _logger.LogInformation(
                    "Retrieved {Count} approvals of type {ApprovalType}",
                    typeApprovals.Count,
                    type
                );
                return Result.Success<IEnumerable<Approval>>(typeApprovals);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting approvals of type {ApprovalType}", type);
                return Result.Failure<IEnumerable<Approval>>(
                    Error.Internal($"Error getting approvals of type {type}")
                );
            }
        }

        public async Task<Result<Approval>> GetApprovalByIdAsync(Guid approvalId)
        {
            try
            {
                var approval = await _context
                    .Approvals.Where(a => !a.IsDeleted && a.Id == approvalId)
                    .Include(a => a.User)
                    .Include(a => a.Processor)
                    .FirstOrDefaultAsync();

                if (approval == null)
                {
                    _logger.LogWarning("Approval {ApprovalId} not found", approvalId);
                    return Result.Failure<Approval>(
                        Error.NotFound($"Approval {approvalId} not found")
                    );
                }

                return Result.Success(approval);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting approval {ApprovalId}", approvalId);
                return Result.Failure<Approval>(
                    Error.Internal($"Error getting approval {approvalId}")
                );
            }
        }

        public async Task<Result<Approval>> CreateApprovalAsync(
            Guid userId,
            ApprovalType type,
            string? approvalData = null,
            Guid? relatedEntityId = null
        )
        {
            try
            {
                var approval = new Approval
                {
                    Id = Guid.NewGuid(),
                    UserId = userId,
                    ApprovalType = type,
                    ApprovalData = approvalData,
                    RelatedEntityId = relatedEntityId,
                    IsApproved = null, // pending (null means pending, true = approved, false = rejected)
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = userId, // User creating their own approval request
                    UpdatedAt = DateTime.UtcNow,
                    IsDeleted = false,
                };

                _context.Approvals.Add(approval);
                await _context.SaveChangesAsync();

                _logger.LogInformation(
                    "Created approval {ApprovalId} for user {UserId} of type {ApprovalType}",
                    approval.Id,
                    userId,
                    type
                );

                return Result.Success(approval);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error creating approval for user {UserId} of type {ApprovalType}",
                    userId,
                    type
                );

                return Result.Failure<Approval>(
                    Error.Internal($"Error creating approval for user {userId}")
                );
            }
        }

        public async Task<Result<Approval>> ApproveAsync(
            Guid approvalId,
            Guid adminId,
            string? notes = null
        )
        {
            try
            {
                var approval = await _context
                    .Approvals.Where(a => !a.IsDeleted && a.Id == approvalId)
                    .Include(a => a.User)
                    .Include(a => a.Processor)
                    .FirstOrDefaultAsync();

                if (approval == null)
                {
                    _logger.LogWarning("Approval {ApprovalId} not found for approval", approvalId);
                    return Result.Failure<Approval>(
                        Error.NotFound($"Approval {approvalId} not found")
                    );
                }

                if (approval.IsApproved.HasValue)
                {
                    _logger.LogWarning(
                        "Approval {ApprovalId} has already been processed",
                        approvalId
                    );
                    return Result.Failure<Approval>(
                        Error.Validation("Approval has already been processed")
                    );
                }

                // Update approval status
                approval.IsApproved = true;
                approval.ProcessedBy = adminId;
                approval.ProcessedAt = DateTime.UtcNow;
                approval.Notes = notes;
                approval.UpdatedAt = DateTime.UtcNow;
                approval.UpdatedBy = adminId;

                _context.Approvals.Update(approval);
                await _context.SaveChangesAsync();
                _logger.LogInformation(
                    "Approval {ApprovalId} approved by admin {AdminId}",
                    approvalId,
                    adminId
                );

                return Result.Success(approval);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error approving approval {ApprovalId} by admin {AdminId}",
                    approvalId,
                    adminId
                );
                return Result.Failure<Approval>(
                    Error.Internal($"Error approving approval {approvalId}")
                );
            }
        }

        public async Task<Result<Approval>> RejectAsync(
            Guid approvalId,
            Guid adminId,
            string rejectionReason,
            string? notes = null
        )
        {
            try
            {
                var approval = await _context
                    .Approvals.Where(a => !a.IsDeleted && a.Id == approvalId)
                    .Include(a => a.User)
                    .Include(a => a.Processor)
                    .FirstOrDefaultAsync();

                if (approval == null)
                {
                    _logger.LogWarning("Approval {ApprovalId} not found for rejection", approvalId);
                    return Result.Failure<Approval>(
                        Error.NotFound($"Approval {approvalId} not found")
                    );
                }

                if (approval.IsApproved.HasValue)
                {
                    _logger.LogWarning(
                        "Approval {ApprovalId} has already been processed",
                        approvalId
                    );
                    return Result.Failure<Approval>(
                        Error.Validation("Approval has already been processed")
                    );
                }

                if (string.IsNullOrWhiteSpace(rejectionReason))
                {
                    return Result.Failure<Approval>(
                        Error.Validation("Rejection reason is required")
                    );
                }

                // Update approval status
                approval.IsApproved = false;
                approval.ProcessedBy = adminId;
                approval.ProcessedAt = DateTime.UtcNow;
                approval.RejectionReason = rejectionReason;
                approval.Notes = notes;
                approval.UpdatedAt = DateTime.UtcNow;
                approval.UpdatedBy = adminId;

                _context.Approvals.Update(approval);
                await _context.SaveChangesAsync();
                _logger.LogInformation(
                    "Approval {ApprovalId} rejected by admin {AdminId} with reason: {RejectionReason}",
                    approvalId,
                    adminId,
                    rejectionReason
                );

                return Result.Success(approval);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error rejecting approval {ApprovalId} by admin {AdminId}",
                    approvalId,
                    adminId
                );
                return Result.Failure<Approval>(
                    Error.Internal($"Error rejecting approval {approvalId}")
                );
            }
        }
    }
}
